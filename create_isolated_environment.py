#!/usr/bin/env python3
"""
Advanced Isolated Environment Creator
Creates a completely isolated environment to prevent data collection
"""

import os
import sys
import json
import random
import string
import tempfile
import subprocess
from datetime import datetime
from pathlib import Path

class IsolatedEnvironmentCreator:
    """Creates isolated environment for safe Augment usage"""
    
    def __init__(self):
        self.temp_dir = None
        self.fake_profile = self.generate_fake_profile()
        self.isolation_active = False
        
    def generate_fake_profile(self):
        """Generate completely fake user profile"""
        fake_names = [
            "DevCoder2024", "CodeMaster", "TechBuilder", "AppDeveloper", 
            "WebCreator", "SoftwareEngineer", "FullStackDev", "BackendPro"
        ]
        
        fake_emails = [
            "<EMAIL>", "<EMAIL>", 
            "<EMAIL>", "<EMAIL>"
        ]
        
        fake_companies = [
            "TechCorp Solutions", "Digital Innovations Ltd", "CodeCraft Studios",
            "DevWorks Inc", "Software Solutions Co", "Tech Dynamics"
        ]
        
        return {
            'username': random.choice(fake_names),
            'email': random.choice(fake_emails),
            'company': random.choice(fake_companies),
            'full_name': f"{random.choice(['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'])} {random.choice(['<PERSON>', '<PERSON>', '<PERSON>', 'Davis'])}",
            'country': random.choice(['United States', 'Canada', 'United Kingdom', 'Germany']),
            'timezone': random.choice(['UTC-5', 'UTC-8', 'UTC+0', 'UTC+1']),
            'language': 'en-US'
        }
    
    def create_isolated_environment(self):
        """Create completely isolated environment"""
        print("🔒 Creating Isolated Environment...")
        print("=" * 50)
        
        try:
            # Create temporary directory
            self.temp_dir = tempfile.mkdtemp(prefix="isolated_augment_")
            print(f"📁 Isolated directory: {self.temp_dir}")
            
            # Create fake VSCode settings
            self.create_fake_vscode_environment()
            
            # Create fake system environment
            self.create_fake_system_environment()
            
            # Create fake user data
            self.create_fake_user_data()
            
            # Setup network isolation
            self.setup_network_isolation()
            
            self.isolation_active = True
            print("✅ Isolated environment created successfully!")
            
            return self.temp_dir
            
        except Exception as e:
            print(f"❌ Failed to create isolated environment: {e}")
            return None
    
    def create_fake_vscode_environment(self):
        """Create fake VSCode environment"""
        print("📦 Creating fake VSCode environment...")
        
        # Create fake VSCode directories
        vscode_dirs = [
            "User/globalStorage",
            "User/workspaceStorage", 
            "User/extensions",
            "logs"
        ]
        
        for dir_path in vscode_dirs:
            full_path = os.path.join(self.temp_dir, "Code", dir_path)
            os.makedirs(full_path, exist_ok=True)
        
        # Create fake settings.json
        fake_settings = {
            "telemetry.telemetryLevel": "off",
            "update.mode": "none",
            "extensions.autoUpdate": False,
            "workbench.enableExperiments": False,
            "workbench.settings.enableNaturalLanguageSearch": False,
            "user.email": self.fake_profile['email'],
            "user.name": self.fake_profile['full_name'],
            "git.defaultCloneDirectory": self.temp_dir
        }
        
        settings_path = os.path.join(self.temp_dir, "Code", "User", "settings.json")
        with open(settings_path, 'w') as f:
            json.dump(fake_settings, f, indent=2)
        
        print("   ✅ Fake VSCode environment created")
    
    def create_fake_system_environment(self):
        """Create fake system environment variables"""
        print("🖥️ Setting up fake system environment...")
        
        # Backup original environment
        self.original_env = {}
        env_vars = [
            'USERNAME', 'COMPUTERNAME', 'USERDOMAIN', 'USERPROFILE',
            'APPDATA', 'LOCALAPPDATA', 'TEMP', 'TMP'
        ]
        
        for var in env_vars:
            if var in os.environ:
                self.original_env[var] = os.environ[var]
        
        # Set fake environment variables
        fake_username = self.fake_profile['username']
        fake_computer = f"DEV-{random.randint(1000, 9999)}"
        
        os.environ['USERNAME'] = fake_username
        os.environ['COMPUTERNAME'] = fake_computer
        os.environ['USERDOMAIN'] = 'ISOLATED-ENV'
        os.environ['USERPROFILE'] = os.path.join(self.temp_dir, "Users", fake_username)
        os.environ['APPDATA'] = os.path.join(self.temp_dir, "AppData", "Roaming")
        os.environ['LOCALAPPDATA'] = os.path.join(self.temp_dir, "AppData", "Local")
        os.environ['TEMP'] = os.path.join(self.temp_dir, "Temp")
        os.environ['TMP'] = os.path.join(self.temp_dir, "Temp")
        
        # Create directories
        for path in [os.environ['USERPROFILE'], os.environ['APPDATA'], 
                    os.environ['LOCALAPPDATA'], os.environ['TEMP']]:
            os.makedirs(path, exist_ok=True)
        
        print(f"   👤 Fake user: {fake_username}")
        print(f"   🖥️ Fake computer: {fake_computer}")
        print("   ✅ Fake system environment set")
    
    def create_fake_user_data(self):
        """Create fake user data files"""
        print("👤 Creating fake user data...")
        
        # Create fake git config
        git_config = f"""[user]
    name = {self.fake_profile['full_name']}
    email = {self.fake_profile['email']}
[core]
    autocrlf = true
[init]
    defaultBranch = main
"""
        
        git_config_path = os.path.join(os.environ['USERPROFILE'], ".gitconfig")
        with open(git_config_path, 'w') as f:
            f.write(git_config)
        
        # Create fake SSH directory
        ssh_dir = os.path.join(os.environ['USERPROFILE'], ".ssh")
        os.makedirs(ssh_dir, exist_ok=True)
        
        # Create fake known_hosts
        known_hosts_path = os.path.join(ssh_dir, "known_hosts")
        with open(known_hosts_path, 'w') as f:
            f.write("# Fake SSH known hosts file\n")
        
        print("   ✅ Fake user data created")
    
    def setup_network_isolation(self):
        """Setup network isolation (placeholder)"""
        print("🌐 Setting up network isolation...")
        
        # Create fake hosts file
        hosts_content = """# Fake hosts file for isolation
127.0.0.1 localhost
127.0.0.1 telemetry.augmentcode.com
127.0.0.1 api.augmentcode.com
127.0.0.1 analytics.augmentcode.com
"""
        
        hosts_path = os.path.join(self.temp_dir, "hosts")
        with open(hosts_path, 'w') as f:
            f.write(hosts_content)
        
        print("   ✅ Network isolation configured")
    
    def launch_isolated_vscode(self, project_path=None):
        """Launch VSCode in isolated environment"""
        if not self.isolation_active:
            print("❌ Isolated environment not active")
            return False
        
        print("🚀 Launching VSCode in isolated environment...")
        
        try:
            # VSCode command with isolated settings
            vscode_cmd = [
                "code",
                "--user-data-dir", os.path.join(self.temp_dir, "Code"),
                "--extensions-dir", os.path.join(self.temp_dir, "Code", "extensions"),
                "--disable-telemetry",
                "--disable-updates",
                "--disable-crash-reporter"
            ]
            
            if project_path:
                vscode_cmd.append(project_path)
            else:
                vscode_cmd.append(self.temp_dir)
            
            # Launch VSCode
            subprocess.Popen(vscode_cmd, cwd=self.temp_dir)
            
            print("✅ VSCode launched in isolated environment")
            print(f"📁 Working directory: {self.temp_dir}")
            print(f"👤 Isolated user: {self.fake_profile['username']}")
            print(f"📧 Isolated email: {self.fake_profile['email']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to launch isolated VSCode: {e}")
            return False
    
    def restore_environment(self):
        """Restore original environment"""
        if not self.isolation_active:
            return
        
        print("🔄 Restoring original environment...")
        
        # Restore environment variables
        for var, value in self.original_env.items():
            os.environ[var] = value
        
        self.isolation_active = False
        print("✅ Original environment restored")
    
    def cleanup_isolation(self):
        """Clean up isolated environment"""
        self.restore_environment()
        
        if self.temp_dir and os.path.exists(self.temp_dir):
            try:
                import shutil
                shutil.rmtree(self.temp_dir)
                print(f"🧹 Cleaned up isolated directory: {self.temp_dir}")
            except Exception as e:
                print(f"⚠️ Could not clean up directory: {e}")
    
    def show_isolation_status(self):
        """Show current isolation status"""
        print("\n" + "=" * 60)
        print("🔒 ISOLATION STATUS")
        print("=" * 60)
        
        status = "🟢 ACTIVE" if self.isolation_active else "🔴 INACTIVE"
        print(f"Isolation Status: {status}")
        
        if self.isolation_active:
            print(f"\n📁 Isolated Directory: {self.temp_dir}")
            print(f"👤 Fake Profile:")
            print(f"   Name: {self.fake_profile['full_name']}")
            print(f"   Username: {self.fake_profile['username']}")
            print(f"   Email: {self.fake_profile['email']}")
            print(f"   Company: {self.fake_profile['company']}")
            
            print(f"\n🖥️ Environment Variables:")
            print(f"   USERNAME: {os.environ.get('USERNAME', 'Not set')}")
            print(f"   COMPUTERNAME: {os.environ.get('COMPUTERNAME', 'Not set')}")
            print(f"   USERPROFILE: {os.environ.get('USERPROFILE', 'Not set')}")

def main():
    """Main function"""
    print("🔒 Advanced Isolated Environment Creator")
    print("=" * 60)
    print("This tool creates a completely isolated environment")
    print("to prevent any data collection from your real system.")
    print()
    
    creator = IsolatedEnvironmentCreator()
    
    try:
        while True:
            print("\n🔧 OPTIONS:")
            print("1. 🔒 Create Isolated Environment")
            print("2. 🚀 Launch Isolated VSCode")
            print("3. 📊 Show Isolation Status")
            print("4. 🔄 Restore Original Environment")
            print("5. 🧹 Cleanup and Exit")
            
            choice = input("\nSelect option (1-5): ").strip()
            
            if choice == '1':
                creator.create_isolated_environment()
            elif choice == '2':
                if creator.isolation_active:
                    project = input("Enter project path (or press Enter for temp dir): ").strip()
                    project_path = project if project else None
                    creator.launch_isolated_vscode(project_path)
                else:
                    print("❌ Please create isolated environment first")
            elif choice == '3':
                creator.show_isolation_status()
            elif choice == '4':
                creator.restore_environment()
            elif choice == '5':
                creator.cleanup_isolation()
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid option")
                
    except KeyboardInterrupt:
        print("\n\n⚠️ Interrupted by user")
        creator.cleanup_isolation()
    except Exception as e:
        print(f"\n❌ Error: {e}")
        creator.cleanup_isolation()

if __name__ == "__main__":
    main()
