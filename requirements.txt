# Requirements for Advanced Privacy Protection Suite
# Augment Cleaner v3.0 & Advanced Privacy Shield v3.0

# Core Dependencies
# ================

# System monitoring and process management
psutil>=5.8.0

# HTTP requests for network monitoring and checks
requests>=2.25.0

# Type hints support
typing>=3.7.0

# Optional Enhanced Features
# =========================

# Enhanced terminal output and colors
colorama>=0.4.4

# Progress bars for long operations
tqdm>=4.60.0

# Advanced networking (optional)
# netifaces>=0.11.0

# Advanced system information (optional)
# py-cpuinfo>=8.0.0

# Built-in Modules Used
# ====================
# The following modules are built into Python and don't need installation:
#
# Core modules:
# - os (operating system interface)
# - sys (system-specific parameters)
# - json (JSON encoder/decoder)
# - sqlite3 (SQLite database interface)
# - shutil (high-level file operations)
# - pathlib (object-oriented filesystem paths)
# - datetime (date and time handling)
# - hashlib (cryptographic hashing)
# - logging (logging facility)
# - re (regular expressions)
# - random (random number generation)
# - string (string operations)
# - subprocess (subprocess management)
# - uuid (UUID objects)
# - base64 (base64 encoding/decoding)
# - socket (network interface)
# - ctypes (foreign function library)
# - urllib.parse (URL parsing)
#
# Threading and concurrency:
# - threading (thread-based parallelism)
# - concurrent.futures (high-level threading interface)
# - time (time-related functions)
#
# Windows-specific:
# - winreg (Windows registry access)

# Installation Instructions
# ========================
#
# Basic installation:
# pip install psutil requests
#
# Full installation with optional features:
# pip install -r requirements.txt
#
# For development:
# pip install psutil requests colorama tqdm
