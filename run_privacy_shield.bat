@echo off
title Advanced Augment Privacy Shield v3.0 - AI-Powered Protection
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                🛡️ ADVANCED PRIVACY SHIELD v3.0 LAUNCHER 🛡️                 ║
echo ║                     AI-Powered Privacy Protection Suite                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Check if required files exist
if not exist "augment_privacy_shield.py" (
    echo ❌ augment_privacy_shield.py not found in current directory
    echo Please ensure all files are in the same folder
    echo.
    pause
    exit /b 1
)

echo ✅ Privacy Shield script found
echo.

REM Check and install requirements
echo 🔧 Checking Python dependencies...
pip show psutil >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing required packages...
    pip install psutil requests
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo Please run: pip install psutil requests
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Dependencies ready
echo.

REM Check for admin privileges
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: Not running as Administrator
    echo Some advanced features may not work properly
    echo Right-click this file and select "Run as administrator" for best results
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        echo Operation cancelled
        pause
        exit /b 1
    )
    echo.
) else (
    echo ✅ Running with Administrator privileges
    echo.
)

REM Display feature overview
echo 🚀 ADVANCED FEATURES AVAILABLE:
echo    • AI-Powered Fake Data Generation
echo    • Real-time Process Monitoring
echo    • Network Traffic Interception
echo    • Database Protection & Injection
echo    • Comprehensive Data Analysis
echo    • Stealth Mode Operation
echo.

REM Ask for operation mode
echo 🔧 SELECT OPERATION MODE:
echo 1. Standard Mode (Full interface)
echo 2. Stealth Mode (Minimal output)
echo 3. Quick Protection (Auto-activate)
echo.
set /p mode="Select mode (1-3) [default: 1]: "

if "%mode%"=="2" (
    echo 🥷 Starting in Stealth Mode...
    python augment_privacy_shield.py --stealth
) else if "%mode%"=="3" (
    echo ⚡ Starting Quick Protection...
    python augment_privacy_shield.py --quick
) else (
    echo 🛡️ Starting Advanced Privacy Shield...
    python augment_privacy_shield.py
)

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Privacy Shield exited with an error
    echo Check the log files in privacy_shield_logs/ for details
) else (
    echo.
    echo ✅ Privacy Shield completed successfully
)

echo.
echo 📊 Check the following for detailed information:
echo    • Logs: privacy_shield_logs/
echo    • Backups: shield_backup_*/
echo    • Reports: shield_backup_*/privacy_shield_report_*.json
echo.
echo Press any key to exit...
pause >nul
