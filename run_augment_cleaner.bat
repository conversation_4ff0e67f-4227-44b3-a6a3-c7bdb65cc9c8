@echo off
title Augment Cleaner v3.0 - Enhanced Privacy Protection
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🧹 AUGMENT CLEANER v3.0 LAUNCHER 🧹                      ║
echo ║                        Enhanced Privacy Protection Suite                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Check if required files exist
if not exist "augment_cleaner_v2.py" (
    echo ❌ augment_cleaner_v2.py not found in current directory
    echo Please ensure all files are in the same folder
    echo.
    pause
    exit /b 1
)

echo ✅ Cleaner script found
echo.

REM Check and install requirements
echo 🔧 Checking Python dependencies...
pip show psutil >nul 2>&1
if errorlevel 1 (
    echo 📦 Installing required packages...
    pip install psutil requests
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo Please run: pip install psutil requests
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Dependencies ready
echo.

REM Check for admin privileges
net session >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: Not running as Administrator
    echo Some features may not work properly
    echo Right-click this file and select "Run as administrator" for best results
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        echo Operation cancelled
        pause
        exit /b 1
    )
    echo.
) else (
    echo ✅ Running with Administrator privileges
    echo.
)

REM Run the cleaner
echo 🚀 Starting Augment Cleaner v3.0...
echo.
python augment_cleaner_v2.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Cleaner exited with an error
    echo Check the log files for details
) else (
    echo.
    echo ✅ Cleaner completed successfully
)

echo.
echo Press any key to exit...
pause >nul
