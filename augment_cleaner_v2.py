import os
import sqlite3
import shutil
import json
import winreg
import subprocess
import sys
import threading
import time
import hashlib
import logging
import psutil
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

class AugmentCleanerV2:
    """Enhanced cleaner for newer Augment versions (0.492.2+) with advanced features"""

    def __init__(self, enable_logging: bool = True, max_workers: int = 4):
        self.findings = {
            'extensions': [],
            'databases': [],
            'personal_data': [],
            'system_fingerprints': [],
            'network_traces': [],
            'cloud_data': [],
            'ai_training_data': [],
            'registry_entries': [],
            'process_data': [],
            'cache_data': [],
            'temp_files': [],
            'browser_data': []
        }
        self.cleaned_items = 0
        self.backup_dir = None
        self.max_workers = max_workers
        self.scan_stats = {
            'total_files_scanned': 0,
            'total_size_mb': 0,
            'scan_duration': 0,
            'threats_detected': 0
        }

        # Setup logging
        if enable_logging:
            self.setup_logging()

        # Security patterns for enhanced detection
        self.security_patterns = {
            'personal_data': [
                r'username', r'user_id', r'email', r'name', r'profile',
                r'identity', r'account', r'login', r'auth', r'token'
            ],
            'system_info': [
                r'hardware', r'cpu', r'gpu', r'memory', r'disk',
                r'machine_id', r'system_id', r'fingerprint'
            ],
            'network_data': [
                r'ip_address', r'mac_address', r'network', r'wifi',
                r'connection', r'proxy', r'dns'
            ],
            'ai_training': [
                r'training', r'model', r'dataset', r'learning',
                r'neural', r'algorithm', r'prediction'
            ]
        }
    
    def setup_logging(self):
        """Setup logging for the cleaner"""
        log_dir = Path("augment_cleaner_logs")
        log_dir.mkdir(exist_ok=True)

        log_file = log_dir / f"cleaner_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def scan_for_newer_augment(self):
        """Comprehensive scan for newer Augment versions and their data with parallel processing"""
        start_time = time.time()

        print("🔍 Enhanced Augment Scanner v3.0 - Advanced Privacy Protection")
        print("=" * 70)

        # Create backup directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.backup_dir = f"augment_backup_{timestamp}"
        os.makedirs(self.backup_dir, exist_ok=True)

        # Check if Augment processes are running
        self.check_running_processes()

        # Parallel scanning for better performance
        scan_tasks = [
            self.scan_extensions,
            self.scan_databases_deep,
            self.scan_personal_data,
            self.scan_system_fingerprints,
            self.scan_cloud_data,
            self.scan_ai_training_data,
            self.scan_registry_deep,
            self.scan_network_traces,
            self.scan_browser_data,
            self.scan_cache_data,
            self.scan_temp_files
        ]

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [executor.submit(task) for task in scan_tasks]

            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    if hasattr(self, 'logger'):
                        self.logger.error(f"Scan task failed: {e}")
                    else:
                        print(f"❌ Scan task failed: {e}")

        # Calculate scan statistics
        self.scan_stats['scan_duration'] = time.time() - start_time
        self.scan_stats['threats_detected'] = sum(len(self.findings[category]) for category in self.findings)

        return self.generate_findings_report()
    
    def check_running_processes(self):
        """Check for running Augment processes"""
        print("\n🔍 Checking for running Augment processes...")

        augment_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if any('augment' in str(item).lower() for item in proc.info['cmdline'] or []):
                    augment_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info['cmdline'] or [])
                    })
                    self.findings['process_data'].append({
                        'type': 'running_process',
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': ' '.join(proc.info['cmdline'] or [])
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if augment_processes:
            print(f"   ⚠️ Found {len(augment_processes)} running Augment processes")
            for proc in augment_processes:
                print(f"      PID {proc['pid']}: {proc['name']}")
        else:
            print("   ✅ No running Augment processes detected")

    def scan_extensions(self):
        """Enhanced scan for Augment extensions with detailed analysis"""
        print("\n📦 Scanning for Augment extensions...")

        vscode_paths = [
            (os.path.expandvars(r"%APPDATA%\Code"), "VSCode"),
            (os.path.expandvars(r"%APPDATA%\Code - Insiders"), "VSCode Insiders"),
            (os.path.expandvars(r"%APPDATA%\Cursor"), "Cursor"),
            (os.path.expanduser("~/.vscode"), "VSCode (User)"),
            (os.path.expandvars(r"%USERPROFILE%\.vscode"), "VSCode (Profile)"),
        ]

        for base_path, ide_name in vscode_paths:
            extensions_dir = os.path.join(base_path, "User", "extensions")
            if not os.path.exists(extensions_dir):
                continue

            for item in os.listdir(extensions_dir):
                if any(pattern in item.lower() for pattern in ['augment', 'augmentcode']):
                    ext_path = os.path.join(extensions_dir, item)
                    version = self.extract_version(item)
                    file_hash = self.calculate_directory_hash(ext_path)

                    # Enhanced extension analysis
                    ext_info = {
                        'ide': ide_name,
                        'name': item,
                        'path': ext_path,
                        'version': version,
                        'is_newer': self.is_newer_version(version),
                        'size_mb': self.get_folder_size_mb(ext_path),
                        'hash': file_hash,
                        'last_modified': self.get_last_modified(ext_path),
                        'suspicious_files': self.scan_suspicious_files(ext_path)
                    }

                    self.findings['extensions'].append(ext_info)
                    self.scan_stats['total_size_mb'] += ext_info['size_mb']

                    print(f"   📦 Found: {item} (v{version}) in {ide_name}")
                    print(f"       Size: {ext_info['size_mb']} MB | Hash: {file_hash[:8]}...")

                    if self.is_newer_version(version):
                        print(f"       🚨 NEWER VERSION - Enhanced data collection!")

                    if ext_info['suspicious_files']:
                        print(f"       ⚠️ {len(ext_info['suspicious_files'])} suspicious files detected")
    
    def scan_databases_deep(self):
        """Deep scan of VSCode databases for personal data"""
        print("\n🗄️ Deep scanning databases for personal data...")
        
        vscode_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage")
        ]
        
        personal_patterns = [
            '%username%', '%user%', '%computer%', '%machine%', '%email%',
            '%identity%', '%profile%', '%account%', '%name%', '%domain%'
        ]
        
        for vscode_path in vscode_paths:
            state_db = os.path.join(vscode_path, "state.vscdb")
            if not os.path.exists(state_db):
                continue
                
            try:
                conn = sqlite3.connect(state_db)
                cur = conn.cursor()
                
                # Check for personal data in database
                personal_entries = []
                for pattern in personal_patterns:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?", 
                              (pattern, pattern))
                    results = cur.fetchall()
                    personal_entries.extend(results)
                
                # Check for actual username in data
                username = os.environ.get('USERNAME', '').lower()
                if username:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(value) LIKE ?", 
                              (f'%{username}%',))
                    username_entries = cur.fetchall()
                    personal_entries.extend(username_entries)
                
                if personal_entries:
                    self.findings['personal_data'].append({
                        'database': state_db,
                        'entries': len(personal_entries),
                        'sample_keys': [entry[0] for entry in personal_entries[:5]],
                        'contains_username': any(username in str(entry[1]).lower() for entry in personal_entries)
                    })
                    
                    print(f"   🚨 Found {len(personal_entries)} personal data entries in {os.path.basename(state_db)}")
                    if any(username in str(entry[1]).lower() for entry in personal_entries):
                        print(f"       ⚠️ Contains your actual username: {username}")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error scanning {state_db}: {str(e)}")
    
    def scan_personal_data(self):
        """Scan for personal data collection"""
        print("\n👤 Scanning for personal data collection...")
        
        # Check workspace storage for personal projects
        workspace_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage")
        ]
        
        for workspace_path in workspace_paths:
            if not os.path.exists(workspace_path):
                continue
                
            for workspace_dir in os.listdir(workspace_path):
                workspace_full = os.path.join(workspace_path, workspace_dir)
                if os.path.isdir(workspace_full):
                    # Check for Augment-related files
                    for file in os.listdir(workspace_full):
                        if 'augment' in file.lower():
                            self.findings['personal_data'].append({
                                'type': 'workspace_data',
                                'path': os.path.join(workspace_full, file),
                                'workspace': workspace_dir
                            })
                            print(f"   📁 Personal workspace data: {file}")
    
    def scan_system_fingerprints(self):
        """Scan for system fingerprinting data"""
        print("\n🖥️ Scanning for system fingerprinting data...")
        
        # Check for hardware fingerprint files
        fingerprint_locations = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%APPDATA%\Augment"),
            os.path.expandvars(r"%TEMP%\Augment")
        ]
        
        for location in fingerprint_locations:
            if os.path.exists(location):
                for root, dirs, files in os.walk(location):
                    for file in files:
                        if any(keyword in file.lower() for keyword in ['hardware', 'system', 'fingerprint', 'machine']):
                            file_path = os.path.join(root, file)
                            self.findings['system_fingerprints'].append({
                                'type': 'hardware_fingerprint',
                                'path': file_path,
                                'size': os.path.getsize(file_path)
                            })
                            print(f"   🖥️ System fingerprint: {file}")
    
    def scan_cloud_data(self):
        """Scan for cloud synchronization data"""
        print("\n☁️ Scanning for cloud synchronization data...")
        
        cloud_patterns = ['sync', 'cloud', 'remote', 'server', 'upload', 'backup']
        
        # Check VSCode logs for cloud activity
        log_paths = [
            os.path.expandvars(r"%APPDATA%\Code\logs"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\logs")
        ]
        
        for log_path in log_paths:
            if os.path.exists(log_path):
                for root, dirs, files in os.walk(log_path):
                    for file in files:
                        if file.endswith('.log'):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    if any(pattern in content.lower() for pattern in cloud_patterns):
                                        self.findings['cloud_data'].append({
                                            'type': 'cloud_activity_log',
                                            'path': file_path,
                                            'suspicious': True
                                        })
                                        print(f"   ☁️ Cloud activity in logs: {file}")
                                        break
                            except Exception:
                                pass
    
    def scan_ai_training_data(self):
        """Scan for AI/ML training data collection"""
        print("\n🤖 Scanning for AI/ML training data...")
        
        ai_patterns = ['training', 'model', 'ml', 'ai', 'neural', 'learning']
        
        # Check extension directories for AI data
        for extension in self.findings['extensions']:
            if extension['is_newer']:
                ext_path = extension['path']
                for root, dirs, files in os.walk(ext_path):
                    for file in files:
                        if any(pattern in file.lower() for pattern in ai_patterns):
                            file_path = os.path.join(root, file)
                            self.findings['ai_training_data'].append({
                                'type': 'ai_training_file',
                                'path': file_path,
                                'extension': extension['name']
                            })
                            print(f"   🤖 AI training data: {file}")
    
    def scan_registry_deep(self):
        """Deep scan of Windows Registry for Augment data"""
        print("\n🗂️ Deep scanning Windows Registry...")
        
        try:
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall")
            ]
            
            for hkey, path in registry_paths:
                try:
                    with winreg.OpenKey(hkey, path) as key:
                        i = 0
                        while True:
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                if 'augment' in subkey_name.lower():
                                    self.findings['registry_entries'].append({
                                        'hkey': 'HKEY_CURRENT_USER' if hkey == winreg.HKEY_CURRENT_USER else 'HKEY_LOCAL_MACHINE',
                                        'path': f"{path}\\{subkey_name}",
                                        'name': subkey_name
                                    })
                                    print(f"   🗂️ Registry entry: {subkey_name}")
                                i += 1
                            except WindowsError:
                                break
                except Exception:
                    continue
        except Exception as e:
            print(f"   ❌ Registry scan error: {str(e)}")
    
    def scan_network_traces(self):
        """Scan for network activity traces"""
        print("\n🌐 Scanning for network traces...")
        
        # Check hosts file
        hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
        try:
            if os.path.exists(hosts_file):
                with open(hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if 'augment' in content.lower():
                        self.findings['network_traces'].append({
                            'type': 'hosts_file_entry',
                            'path': hosts_file
                        })
                        print("   🌐 Found Augment entries in hosts file")
        except Exception:
            pass
    
    def calculate_directory_hash(self, directory_path: str) -> str:
        """Calculate SHA256 hash of directory contents"""
        try:
            hash_sha256 = hashlib.sha256()
            for root, _, files in os.walk(directory_path):
                for filename in sorted(files):
                    filepath = os.path.join(root, filename)
                    if os.path.exists(filepath):
                        with open(filepath, 'rb') as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return "unknown"

    def get_last_modified(self, path: str) -> str:
        """Get last modified time of file/directory"""
        try:
            timestamp = os.path.getmtime(path)
            return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            return "unknown"

    def scan_suspicious_files(self, directory_path: str) -> List[str]:
        """Scan for suspicious files in directory"""
        suspicious_files = []
        suspicious_extensions = ['.exe', '.dll', '.bat', '.ps1', '.vbs', '.scr']
        suspicious_names = ['keylog', 'capture', 'monitor', 'track', 'collect']

        try:
            for root, _, files in os.walk(directory_path):
                for filename in files:
                    filepath = os.path.join(root, filename)

                    # Check file extension
                    if any(filename.lower().endswith(ext) for ext in suspicious_extensions):
                        suspicious_files.append(filepath)

                    # Check filename patterns
                    elif any(pattern in filename.lower() for pattern in suspicious_names):
                        suspicious_files.append(filepath)

        except Exception:
            pass

        return suspicious_files

    def scan_browser_data(self):
        """Scan browser data for Augment traces"""
        print("\n🌐 Scanning browser data for Augment traces...")

        browser_paths = {
            'Chrome': os.path.expandvars(r"%LOCALAPPDATA%\Google\Chrome\User Data"),
            'Edge': os.path.expandvars(r"%LOCALAPPDATA%\Microsoft\Edge\User Data"),
            'Firefox': os.path.expandvars(r"%APPDATA%\Mozilla\Firefox\Profiles")
        }

        for browser_name, browser_path in browser_paths.items():
            if os.path.exists(browser_path):
                self.scan_browser_extensions(browser_path, browser_name)
                self.scan_browser_storage(browser_path, browser_name)

    def scan_browser_extensions(self, browser_path: str, browser_name: str):
        """Scan browser extensions for Augment"""
        try:
            extensions_path = os.path.join(browser_path, "Default", "Extensions")
            if os.path.exists(extensions_path):
                for ext_id in os.listdir(extensions_path):
                    ext_path = os.path.join(extensions_path, ext_id)
                    if os.path.isdir(ext_path):
                        # Check manifest files for Augment references
                        for version_dir in os.listdir(ext_path):
                            manifest_path = os.path.join(ext_path, version_dir, "manifest.json")
                            if os.path.exists(manifest_path):
                                try:
                                    with open(manifest_path, 'r', encoding='utf-8') as f:
                                        content = f.read()
                                        if 'augment' in content.lower():
                                            self.findings['browser_data'].append({
                                                'type': 'browser_extension',
                                                'browser': browser_name,
                                                'extension_id': ext_id,
                                                'path': ext_path
                                            })
                                            print(f"   🌐 Found Augment extension in {browser_name}: {ext_id}")
                                except Exception:
                                    pass
        except Exception:
            pass

    def scan_browser_storage(self, browser_path: str, browser_name: str):
        """Scan browser local storage for Augment data"""
        try:
            storage_paths = [
                os.path.join(browser_path, "Default", "Local Storage"),
                os.path.join(browser_path, "Default", "Session Storage"),
                os.path.join(browser_path, "Default", "IndexedDB")
            ]

            for storage_path in storage_paths:
                if os.path.exists(storage_path):
                    for item in os.listdir(storage_path):
                        if 'augment' in item.lower():
                            item_path = os.path.join(storage_path, item)
                            self.findings['browser_data'].append({
                                'type': 'browser_storage',
                                'browser': browser_name,
                                'storage_type': os.path.basename(storage_path),
                                'path': item_path
                            })
                            print(f"   🌐 Found Augment data in {browser_name} storage: {item}")
        except Exception:
            pass

    def scan_cache_data(self):
        """Scan for cached Augment data"""
        print("\n💾 Scanning for cached Augment data...")

        cache_locations = [
            os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
            os.path.expandvars(r"%TEMP%"),
            os.path.expandvars(r"%APPDATA%\Code\CachedExtensions"),
            os.path.expandvars(r"%APPDATA%\Code\logs"),
            os.path.expandvars(r"%LOCALAPPDATA%\Programs\Microsoft VS Code\resources\app\extensions")
        ]

        for cache_location in cache_locations:
            if os.path.exists(cache_location):
                try:
                    for item in os.listdir(cache_location):
                        if 'augment' in item.lower():
                            item_path = os.path.join(cache_location, item)
                            self.findings['cache_data'].append({
                                'type': 'cache_file',
                                'path': item_path,
                                'size_mb': self.get_folder_size_mb(item_path) if os.path.isdir(item_path) else round(os.path.getsize(item_path) / (1024 * 1024), 2)
                            })
                            print(f"   💾 Found cached data: {item}")
                except Exception:
                    pass

    def scan_temp_files(self):
        """Scan for temporary Augment files"""
        print("\n🗂️ Scanning for temporary Augment files...")

        temp_locations = [
            os.path.expandvars(r"%TEMP%"),
            os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
            os.path.expandvars(r"%USERPROFILE%\AppData\Local\Temp")
        ]

        for temp_location in temp_locations:
            if os.path.exists(temp_location):
                try:
                    for item in os.listdir(temp_location):
                        if any(pattern in item.lower() for pattern in ['augment', 'vscode-augment', 'augmentcode']):
                            item_path = os.path.join(temp_location, item)
                            if os.path.exists(item_path):
                                self.findings['temp_files'].append({
                                    'type': 'temp_file',
                                    'path': item_path,
                                    'size_mb': self.get_folder_size_mb(item_path) if os.path.isdir(item_path) else round(os.path.getsize(item_path) / (1024 * 1024), 2)
                                })
                                print(f"   🗂️ Found temp file: {item}")
                except Exception:
                    pass

    def extract_version(self, extension_name):
        """Extract version from extension name with improved regex"""
        import re
        version_patterns = [
            r'(\d+\.\d+\.\d+)',
            r'v(\d+\.\d+\.\d+)',
            r'-(\d+\.\d+\.\d+)',
        ]

        for pattern in version_patterns:
            match = re.search(pattern, extension_name)
            if match:
                return match.group(1)
        return "0.0.0"

    def is_newer_version(self, version):
        """Check if version is newer than 0.490.0 with better parsing"""
        try:
            parts = [int(x) for x in version.split('.')]
            if len(parts) >= 3:
                return parts[0] > 0 or (parts[0] == 0 and parts[1] >= 490)
            return False
        except:
            return False

    def get_folder_size_mb(self, folder_path):
        """Get folder size in MB with better error handling"""
        try:
            total_size = 0
            for dirpath, _, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        try:
                            total_size += os.path.getsize(filepath)
                        except (OSError, IOError):
                            continue
            return round(total_size / (1024 * 1024), 2)
        except Exception:
            return 0
    
    def generate_findings_report(self):
        """Generate comprehensive findings report with detailed statistics"""
        print("\n" + "=" * 80)
        print("📊 ADVANCED AUGMENT DETECTION REPORT v3.0")
        print("=" * 80)

        total_items = sum(len(self.findings[category]) for category in self.findings)

        # Scan statistics
        print(f"⏱️  Scan Duration: {self.scan_stats['scan_duration']:.2f} seconds")
        print(f"📁 Files Scanned: {self.scan_stats['total_files_scanned']}")
        print(f"💾 Total Size: {self.scan_stats['total_size_mb']:.2f} MB")
        print(f"🎯 Threats Detected: {total_items}")

        if total_items == 0:
            print("\n✅ No Augment installations or data found. Your system appears clean!")
            return False

        # Detailed breakdown by category
        print(f"\n📋 DETAILED BREAKDOWN:")
        print("-" * 50)

        categories_info = {
            'extensions': ('📦', 'Extensions'),
            'databases': ('🗄️', 'Database Entries'),
            'personal_data': ('👤', 'Personal Data'),
            'system_fingerprints': ('🖥️', 'System Fingerprints'),
            'network_traces': ('🌐', 'Network Traces'),
            'cloud_data': ('☁️', 'Cloud Data'),
            'ai_training_data': ('🤖', 'AI Training Data'),
            'registry_entries': ('🗂️', 'Registry Entries'),
            'process_data': ('⚙️', 'Process Data'),
            'cache_data': ('💾', 'Cache Data'),
            'temp_files': ('🗂️', 'Temporary Files'),
            'browser_data': ('🌐', 'Browser Data')
        }

        for category, (icon, name) in categories_info.items():
            count = len(self.findings[category])
            if count > 0:
                print(f"{icon} {name}: {count} items")

                # Show sample items for important categories
                if category in ['extensions', 'personal_data', 'system_fingerprints']:
                    for item in self.findings[category][:3]:  # Show first 3 items
                        if category == 'extensions':
                            print(f"    • {item['name']} (v{item['version']}) - {item['size_mb']} MB")
                        elif category == 'personal_data' and 'database' in item:
                            print(f"    • {os.path.basename(item['database'])} - {item['entries']} entries")
                        elif category == 'system_fingerprints':
                            print(f"    • {os.path.basename(item['path'])}")

                    if count > 3:
                        print(f"    ... and {count - 3} more items")

        # Security risk assessment
        print(f"\n🚨 SECURITY RISK ASSESSMENT:")
        print("-" * 50)

        risk_level = self.calculate_risk_level()
        risk_colors = {
            'LOW': '🟢',
            'MEDIUM': '🟡',
            'HIGH': '🟠',
            'CRITICAL': '🔴'
        }

        print(f"{risk_colors[risk_level]} Risk Level: {risk_level}")

        # Show newer version warnings
        newer_extensions = [ext for ext in self.findings['extensions'] if ext['is_newer']]
        if newer_extensions:
            print(f"\n🚨 PRIVACY ALERT: {len(newer_extensions)} newer Augment version(s) detected!")
            print("   These versions collect significantly more personal data including:")
            print("   • System hardware fingerprinting")
            print("   • User behavior tracking")
            print("   • Code analysis and storage")
            print("   • Network activity monitoring")

        # Show personal data concerns
        personal_items = len(self.findings['personal_data'])
        if personal_items > 0:
            print(f"\n👤 PERSONAL DATA EXPOSURE: {personal_items} instances found")
            username_exposed = any('contains_username' in item and item['contains_username']
                                 for item in self.findings['personal_data'] if isinstance(item, dict))
            if username_exposed:
                print("   ⚠️ Your actual username was found in stored data!")

        # Show system fingerprinting
        fingerprint_items = len(self.findings['system_fingerprints'])
        if fingerprint_items > 0:
            print(f"\n🖥️ SYSTEM FINGERPRINTING: {fingerprint_items} hardware fingerprint files found")
            print("   This data can be used to uniquely identify your computer")

        # Show running processes
        process_items = len(self.findings['process_data'])
        if process_items > 0:
            print(f"\n⚙️ ACTIVE PROCESSES: {process_items} Augment processes currently running")
            print("   Recommendation: Close these processes before cleaning")

        print(f"\n💡 RECOMMENDATIONS:")
        print("-" * 50)
        if risk_level in ['HIGH', 'CRITICAL']:
            print("• Immediate cleaning recommended")
            print("• Consider blocking Augment domains in firewall")
            print("• Review and revoke any granted permissions")
        elif risk_level == 'MEDIUM':
            print("• Cleaning recommended for privacy protection")
            print("• Monitor for future installations")
        else:
            print("• Optional cleaning for complete privacy")

        return True

    def calculate_risk_level(self) -> str:
        """Calculate security risk level based on findings"""
        score = 0

        # Extensions (especially newer versions)
        newer_extensions = [ext for ext in self.findings['extensions'] if ext['is_newer']]
        score += len(newer_extensions) * 3
        score += len(self.findings['extensions']) * 1

        # Personal data exposure
        score += len(self.findings['personal_data']) * 2

        # System fingerprinting
        score += len(self.findings['system_fingerprints']) * 2

        # Active processes
        score += len(self.findings['process_data']) * 1

        # AI training data
        score += len(self.findings['ai_training_data']) * 2

        # Network traces
        score += len(self.findings['network_traces']) * 1

        if score >= 15:
            return 'CRITICAL'
        elif score >= 10:
            return 'HIGH'
        elif score >= 5:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def clean_all_findings(self, force_clean: bool = False):
        """Clean all found Augment data with enhanced removal and safety checks"""
        if not any(self.findings.values()):
            print("✅ No Augment data found to clean.")
            return 0

        print("\n🧹 Starting advanced Augment removal...")
        print("=" * 60)

        # Check for running processes first
        if self.findings['process_data'] and not force_clean:
            print("⚠️ Warning: Augment processes are currently running!")
            print("Recommendation: Close these processes first for complete removal:")
            for proc in self.findings['process_data']:
                print(f"   • PID {proc['pid']}: {proc['name']}")

            response = input("\nContinue anyway? (y/n): ").strip().lower()
            if response not in ['y', 'yes']:
                print("❌ Cleaning cancelled.")
                return 0

        # Create detailed backup manifest
        self.create_backup_manifest()

        # Clean different categories with progress tracking
        cleaning_tasks = [
            ('extensions', self.clean_extensions_batch),
            ('personal_data', self.clean_personal_data_batch),
            ('system_fingerprints', self.clean_fingerprints_batch),
            ('cloud_data', self.clean_cloud_data_batch),
            ('ai_training_data', self.clean_ai_data_batch),
            ('registry_entries', self.clean_registry_batch),
            ('browser_data', self.clean_browser_data_batch),
            ('cache_data', self.clean_cache_data_batch),
            ('temp_files', self.clean_temp_files_batch),
            ('process_data', self.clean_processes_batch)
        ]

        total_tasks = len([task_name for task_name, _ in cleaning_tasks if self.findings[task_name]])
        completed_tasks = 0

        for task_name, clean_function in cleaning_tasks:
            if self.findings[task_name]:
                print(f"\n🧹 Cleaning {task_name.replace('_', ' ').title()}...")
                try:
                    cleaned_count = clean_function()
                    completed_tasks += 1
                    progress = (completed_tasks / total_tasks) * 100
                    print(f"   ✅ Cleaned {cleaned_count} items ({progress:.1f}% complete)")
                except Exception as e:
                    print(f"   ❌ Error cleaning {task_name}: {str(e)}")
                    if hasattr(self, 'logger'):
                        self.logger.error(f"Error cleaning {task_name}: {e}")

        # Final cleanup and verification
        self.verify_cleanup()

        print(f"\n🎉 Advanced cleaning completed!")
        print(f"✅ Total items removed: {self.cleaned_items}")
        print(f"💾 Backups saved to: {self.backup_dir}")
        print(f"📋 Backup manifest: {os.path.join(self.backup_dir, 'manifest.json')}")

        return self.cleaned_items

    def create_backup_manifest(self):
        """Create a detailed manifest of what will be backed up"""
        manifest = {
            'timestamp': datetime.now().isoformat(),
            'cleaner_version': '3.0',
            'backup_directory': self.backup_dir,
            'findings_summary': {category: len(items) for category, items in self.findings.items()},
            'detailed_findings': self.findings,
            'scan_stats': self.scan_stats
        }

        manifest_path = os.path.join(self.backup_dir, 'manifest.json')
        try:
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest, f, indent=2, default=str)
            print(f"📋 Backup manifest created: {manifest_path}")
        except Exception as e:
            print(f"⚠️ Could not create manifest: {e}")

    def verify_cleanup(self):
        """Verify that cleanup was successful"""
        print("\n🔍 Verifying cleanup...")

        verification_failed = []

        # Check if extensions still exist
        for ext in self.findings['extensions']:
            if os.path.exists(ext['path']):
                verification_failed.append(f"Extension still exists: {ext['name']}")

        # Check if fingerprint files still exist
        for fingerprint in self.findings['system_fingerprints']:
            if os.path.exists(fingerprint['path']):
                verification_failed.append(f"Fingerprint file still exists: {os.path.basename(fingerprint['path'])}")

        if verification_failed:
            print("⚠️ Some items could not be completely removed:")
            for item in verification_failed:
                print(f"   • {item}")
        else:
            print("✅ Cleanup verification successful!")

    def clean_extensions_batch(self) -> int:
        """Clean all extensions in batch"""
        cleaned = 0
        for ext in self.findings['extensions']:
            if self.clean_extension(ext):
                cleaned += 1
        return cleaned

    def clean_personal_data_batch(self) -> int:
        """Clean all personal data in batch"""
        cleaned = 0
        for data_item in self.findings['personal_data']:
            if 'database' in data_item:
                if self.clean_database_personal_data(data_item):
                    cleaned += 1
            else:
                if self.clean_personal_data_item(data_item):
                    cleaned += 1
        return cleaned

    def clean_fingerprints_batch(self) -> int:
        """Clean all fingerprint files in batch"""
        cleaned = 0
        for fingerprint in self.findings['system_fingerprints']:
            if self.clean_system_fingerprint(fingerprint):
                cleaned += 1
        return cleaned

    def clean_cloud_data_batch(self) -> int:
        """Clean all cloud data in batch"""
        cleaned = 0
        for cloud_item in self.findings['cloud_data']:
            if self.clean_cloud_data(cloud_item):
                cleaned += 1
        return cleaned

    def clean_ai_data_batch(self) -> int:
        """Clean all AI training data in batch"""
        cleaned = 0
        for ai_item in self.findings['ai_training_data']:
            if self.clean_ai_data(ai_item):
                cleaned += 1
        return cleaned

    def clean_registry_batch(self) -> int:
        """Clean all registry entries in batch"""
        cleaned = 0
        for reg_entry in self.findings['registry_entries']:
            if self.clean_registry_entry(reg_entry):
                cleaned += 1
        return cleaned

    def clean_browser_data_batch(self) -> int:
        """Clean all browser data in batch"""
        cleaned = 0
        for browser_item in self.findings['browser_data']:
            if self.clean_browser_data_item(browser_item):
                cleaned += 1
        return cleaned

    def clean_cache_data_batch(self) -> int:
        """Clean all cache data in batch"""
        cleaned = 0
        for cache_item in self.findings['cache_data']:
            if self.clean_cache_data_item(cache_item):
                cleaned += 1
        return cleaned

    def clean_temp_files_batch(self) -> int:
        """Clean all temporary files in batch"""
        cleaned = 0
        for temp_item in self.findings['temp_files']:
            if self.clean_temp_file_item(temp_item):
                cleaned += 1
        return cleaned

    def clean_processes_batch(self) -> int:
        """Terminate Augment processes"""
        cleaned = 0
        for proc_item in self.findings['process_data']:
            if self.terminate_process(proc_item):
                cleaned += 1
        return cleaned
    
    def clean_extension(self, ext_info) -> bool:
        """Clean extension with enhanced backup and verification"""
        try:
            ext_path = ext_info['path']
            if not os.path.exists(ext_path):
                return False

            # Create backup with metadata
            backup_path = os.path.join(self.backup_dir, f"extension_{ext_info['name']}")
            shutil.copytree(ext_path, backup_path)

            # Save extension metadata
            metadata = {
                'original_path': ext_path,
                'backup_time': datetime.now().isoformat(),
                'extension_info': ext_info
            }

            metadata_path = os.path.join(backup_path, 'backup_metadata.json')
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, default=str)

            # Remove extension
            shutil.rmtree(ext_path)
            self.cleaned_items += 1

            if hasattr(self, 'logger'):
                self.logger.info(f"Removed extension: {ext_info['name']}")

            return True

        except Exception as e:
            error_msg = f"Failed to remove extension {ext_info['name']}: {str(e)}"
            print(f"   ❌ {error_msg}")
            if hasattr(self, 'logger'):
                self.logger.error(error_msg)
            return False

    def clean_personal_data_item(self, data_item) -> bool:
        """Clean personal data item"""
        try:
            if 'path' in data_item and os.path.exists(data_item['path']):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"personal_data_{os.path.basename(data_item['path'])}")
                if os.path.isdir(data_item['path']):
                    shutil.copytree(data_item['path'], backup_path)
                    shutil.rmtree(data_item['path'])
                else:
                    shutil.copy2(data_item['path'], backup_path)
                    os.remove(data_item['path'])

                self.cleaned_items += 1
                return True
        except Exception as e:
            print(f"   ❌ Failed to clean personal data: {str(e)}")
            return False
        return False

    def clean_browser_data_item(self, browser_item) -> bool:
        """Clean browser data item"""
        try:
            if 'path' in browser_item and os.path.exists(browser_item['path']):
                # Create backup
                backup_name = f"browser_{browser_item['browser']}_{os.path.basename(browser_item['path'])}"
                backup_path = os.path.join(self.backup_dir, backup_name)

                if os.path.isdir(browser_item['path']):
                    shutil.copytree(browser_item['path'], backup_path)
                    shutil.rmtree(browser_item['path'])
                else:
                    shutil.copy2(browser_item['path'], backup_path)
                    os.remove(browser_item['path'])

                self.cleaned_items += 1
                return True
        except Exception as e:
            print(f"   ❌ Failed to clean browser data: {str(e)}")
            return False
        return False

    def clean_cache_data_item(self, cache_item) -> bool:
        """Clean cache data item"""
        try:
            if 'path' in cache_item and os.path.exists(cache_item['path']):
                # Create backup
                backup_name = f"cache_{os.path.basename(cache_item['path'])}"
                backup_path = os.path.join(self.backup_dir, backup_name)

                if os.path.isdir(cache_item['path']):
                    shutil.copytree(cache_item['path'], backup_path)
                    shutil.rmtree(cache_item['path'])
                else:
                    shutil.copy2(cache_item['path'], backup_path)
                    os.remove(cache_item['path'])

                self.cleaned_items += 1
                return True
        except Exception as e:
            print(f"   ❌ Failed to clean cache data: {str(e)}")
            return False
        return False

    def clean_temp_file_item(self, temp_item) -> bool:
        """Clean temporary file item"""
        try:
            if 'path' in temp_item and os.path.exists(temp_item['path']):
                # Create backup
                backup_name = f"temp_{os.path.basename(temp_item['path'])}"
                backup_path = os.path.join(self.backup_dir, backup_name)

                if os.path.isdir(temp_item['path']):
                    shutil.copytree(temp_item['path'], backup_path)
                    shutil.rmtree(temp_item['path'])
                else:
                    shutil.copy2(temp_item['path'], backup_path)
                    os.remove(temp_item['path'])

                self.cleaned_items += 1
                return True
        except Exception as e:
            print(f"   ❌ Failed to clean temp file: {str(e)}")
            return False
        return False

    def terminate_process(self, proc_item) -> bool:
        """Terminate Augment process"""
        try:
            pid = proc_item['pid']
            proc = psutil.Process(pid)
            proc.terminate()

            # Wait for process to terminate
            try:
                proc.wait(timeout=5)
                print(f"   ✅ Terminated process: {proc_item['name']} (PID: {pid})")
                self.cleaned_items += 1
                return True
            except psutil.TimeoutExpired:
                # Force kill if terminate doesn't work
                proc.kill()
                print(f"   ✅ Force killed process: {proc_item['name']} (PID: {pid})")
                self.cleaned_items += 1
                return True

        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"   ⚠️ Could not terminate process {proc_item['name']}: {str(e)}")
            return False
        except Exception as e:
            print(f"   ❌ Error terminating process: {str(e)}")
            return False
    
    def clean_database_personal_data(self, db_info):
        """Clean personal data from databases"""
        try:
            db_path = db_info['database']
            if os.path.exists(db_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"database_{os.path.basename(db_path)}")
                shutil.copy2(db_path, backup_path)
                
                # Remove personal data entries
                conn = sqlite3.connect(db_path)
                cur = conn.cursor()
                
                # Remove entries containing personal data
                personal_patterns = ['%augment%', '%username%', '%user%', '%computer%']
                for pattern in personal_patterns:
                    cur.execute("DELETE FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?", 
                              (pattern, pattern))
                
                conn.commit()
                conn.close()
                self.cleaned_items += 1
                print(f"   ✅ Cleaned personal data from: {os.path.basename(db_path)}")
        except Exception as e:
            print(f"   ❌ Failed to clean database: {str(e)}")
    
    def clean_system_fingerprint(self, fingerprint_info):
        """Clean system fingerprint files"""
        try:
            file_path = fingerprint_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"fingerprint_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove file
                os.remove(file_path)
                self.cleaned_items += 1
                print(f"   ✅ Removed fingerprint file: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to remove fingerprint: {str(e)}")
    
    def clean_cloud_data(self, cloud_info):
        """Clean cloud synchronization data"""
        try:
            file_path = cloud_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"cloud_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove or clean file
                if cloud_info['type'] == 'cloud_activity_log':
                    # Clear log content instead of deleting
                    with open(file_path, 'w') as f:
                        f.write("")
                else:
                    os.remove(file_path)
                
                self.cleaned_items += 1
                print(f"   ✅ Cleaned cloud data: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to clean cloud data: {str(e)}")
    
    def clean_ai_data(self, ai_info):
        """Clean AI training data"""
        try:
            file_path = ai_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"ai_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove AI training file
                os.remove(file_path)
                self.cleaned_items += 1
                print(f"   ✅ Removed AI training data: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to remove AI data: {str(e)}")
    
    def clean_registry_entry(self, reg_info):
        """Clean registry entries"""
        try:
            # This is a placeholder - registry cleaning requires careful handling
            print(f"   ⚠️ Registry entry found (manual removal recommended): {reg_info['name']}")
        except Exception as e:
            print(f"   ❌ Registry cleaning error: {str(e)}")

def show_banner():
    """Show enhanced banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🧹 AUGMENT CLEANER v3.0 ADVANCED 🧹                      ║
║                        Enhanced Privacy Protection Suite                      ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🎯 Advanced Detection    🔒 Privacy Protection    💾 Safe Backup System    ║
║  🚀 Parallel Processing   📊 Detailed Reports      ⚡ Real-time Monitoring  ║
║  🛡️ System Fingerprints   🤖 AI Data Detection     🌐 Network Trace Removal ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def get_user_preferences():
    """Get user cleaning preferences"""
    print("\n🔧 CLEANING PREFERENCES:")
    print("-" * 40)

    preferences = {}

    # Ask about logging
    log_response = input("Enable detailed logging? (y/n) [default: y]: ").strip().lower()
    preferences['enable_logging'] = log_response in ['y', 'yes', '']

    # Ask about process termination
    proc_response = input("Automatically terminate running Augment processes? (y/n) [default: n]: ").strip().lower()
    preferences['terminate_processes'] = proc_response in ['y', 'yes']

    # Ask about registry cleaning
    reg_response = input("Include registry cleaning? (y/n) [default: y]: ").strip().lower()
    preferences['clean_registry'] = reg_response in ['y', 'yes', '']

    # Ask about browser data
    browser_response = input("Clean browser data (extensions, storage)? (y/n) [default: y]: ").strip().lower()
    preferences['clean_browser'] = browser_response in ['y', 'yes', '']

    return preferences

def show_cleaning_summary(cleaner, cleaned_count):
    """Show detailed cleaning summary"""
    print("\n" + "=" * 80)
    print("🎉 CLEANING COMPLETED SUCCESSFULLY!")
    print("=" * 80)

    print(f"✅ Total items removed: {cleaned_count}")
    print(f"💾 Backup directory: {cleaner.backup_dir}")
    print(f"📋 Backup manifest: {os.path.join(cleaner.backup_dir, 'manifest.json')}")

    # Show breakdown by category
    categories_cleaned = {
        category: len(items) for category, items in cleaner.findings.items()
        if items and category != 'process_data'  # Processes are terminated, not backed up
    }

    if categories_cleaned:
        print(f"\n📊 ITEMS CLEANED BY CATEGORY:")
        print("-" * 40)
        for category, count in categories_cleaned.items():
            if count > 0:
                category_name = category.replace('_', ' ').title()
                print(f"   {category_name}: {count} items")

    # Security recommendations
    print(f"\n🛡️ SECURITY RECOMMENDATIONS:")
    print("-" * 40)
    print("• Restart your IDE to ensure all changes take effect")
    print("• Consider using a firewall to block Augment domains")
    print("• Regularly run this cleaner to maintain privacy")
    print("• Review IDE extension permissions before installing")

    # Backup information
    print(f"\n💾 BACKUP INFORMATION:")
    print("-" * 40)
    print(f"• All removed items have been safely backed up")
    print(f"• Backup location: {cleaner.backup_dir}")
    print(f"• To restore: Copy items back to their original locations")
    print(f"• Backup size: {cleaner.get_folder_size_mb(cleaner.backup_dir)} MB")

def check_system_requirements():
    """Check if system meets requirements"""
    print("🔍 Checking system requirements...")

    # Check Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
        print("❌ Python 3.7+ required")
        return False

    # Check required modules
    required_modules = ['psutil', 'requests']
    missing_modules = []

    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)

    if missing_modules:
        print(f"❌ Missing required modules: {', '.join(missing_modules)}")
        print("Install with: pip install " + " ".join(missing_modules))
        return False

    print("✅ System requirements met")
    return True

def main():
    """Enhanced main function with advanced features"""
    show_banner()

    # Check system requirements
    if not check_system_requirements():
        print("\nPress Enter to exit...")
        input()
        return

    # Get user preferences
    preferences = get_user_preferences()

    # Initialize cleaner with preferences
    cleaner = AugmentCleanerV2(
        enable_logging=preferences['enable_logging'],
        max_workers=4
    )

    try:
        print(f"\n🔍 Starting comprehensive Augment scan...")
        print("This may take a few moments...")

        # Scan for Augment data
        found_items = cleaner.scan_for_newer_augment()

        if not found_items:
            print("\n✅ No Augment installations found. Your system appears clean!")
            return

        # Show cleaning options
        print(f"\n⚠️ PRIVACY ALERT: Augment data detected on your system!")
        print("This data may include:")
        print("• Personal information and usernames")
        print("• System hardware fingerprints")
        print("• Code analysis and usage patterns")
        print("• Network activity traces")

        # Confirm cleaning
        response = input(f"\nProceed with advanced cleaning? (y/yes or n/no): ").strip().lower()

        if response in ['y', 'yes']:
            # Clean with user preferences
            force_clean = preferences['terminate_processes']
            cleaned_count = cleaner.clean_all_findings(force_clean=force_clean)

            if cleaned_count > 0:
                show_cleaning_summary(cleaner, cleaned_count)

                # Offer to open backup directory
                open_backup = input(f"\nOpen backup directory? (y/n): ").strip().lower()
                if open_backup in ['y', 'yes']:
                    try:
                        os.startfile(cleaner.backup_dir)
                    except Exception:
                        print(f"Backup directory: {cleaner.backup_dir}")
            else:
                print("\n⚠️ No items were cleaned. Check the log for details.")

        else:
            print("\n❌ Cleaning cancelled. No changes made.")
            print("Your privacy remains at risk from detected Augment data.")

    except KeyboardInterrupt:
        print("\n\n❌ Operation cancelled by user.")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        if hasattr(cleaner, 'logger'):
            cleaner.logger.error(f"Unexpected error in main: {e}")

    finally:
        print(f"\n" + "=" * 80)
        print("Thank you for using Augment Cleaner v3.0!")
        print("Stay safe and protect your privacy! 🔒")
        print("=" * 80)
        print("\nPress Enter to exit...")
        input()

if __name__ == "__main__":
    main()
